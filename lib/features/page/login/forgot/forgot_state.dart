part of 'forgot_cubit.dart';

class ForgotState extends Equatable {
  final CaptchaType captchaType;
  final LoginType forgotType;
  final String username;
  final String phone;
  final String email;
  final String password;
  final String confirmPassword;
  final String smsCode;
  final bool isPasswordVisible;
  final TextEditingController phoneController;
  final TextEditingController emailController;
  final TextEditingController smsCodeController;
  final TextEditingController passwordController;
  final TextEditingController confirmPasswordController;
  final SimplyNetStatus forgotStatus;
  final Country? selectedCountry;
  const ForgotState({
    this.captchaType = CaptchaType.wangYi,
    this.forgotType = LoginType.phone,
    this.username = '',
    this.phone = '',
    this.email = '',
    this.password = '',
    this.confirmPassword = '',
    this.smsCode = '',
    this.isPasswordVisible = false,
    required this.phoneController,
    required this.emailController,
    required this.smsCodeController,
    required this.passwordController,
    required this.confirmPasswordController,
    this.forgotStatus = SimplyNetStatus.idle,
    this.selectedCountry,
  });

  @override
  List<Object?> get props => [
        captchaType,
        forgotType,
        username,
        phone,
        email,
        password,
        confirmPassword,
        smsCode,
        isPasswordVisible,
        phoneController,
        emailController,
        smsCodeController,
        passwordController,
        confirmPasswordController,
        forgotStatus,
        selectedCountry,
      ];

  ForgotState copyWith({
    CaptchaType? captchaType,
    LoginType? forgotType,
    String? username,
    String? phone,
    String? email,
    String? password,
    String? confirmPassword,
    String? smsCode,
    bool? isPasswordVisible,
    TextEditingController? phoneController,
    TextEditingController? emailController,
    TextEditingController? smsCodeController,
    TextEditingController? passwordController,
    TextEditingController? confirmPasswordController,
    SimplyNetStatus? forgotStatus,
    Country? selectedCountry,
  }) {
    return ForgotState(
      captchaType: captchaType ?? this.captchaType,
      forgotType: forgotType ?? this.forgotType,
      username: username ?? this.username,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      password: password ?? this.password,
      confirmPassword: confirmPassword ?? this.confirmPassword,
      smsCode: smsCode ?? this.smsCode,
      isPasswordVisible: isPasswordVisible ?? this.isPasswordVisible,
      phoneController: phoneController ?? this.phoneController,
      emailController: emailController ?? this.emailController,
      smsCodeController: smsCodeController ?? this.smsCodeController,
      passwordController: passwordController ?? this.passwordController,
      confirmPasswordController: confirmPasswordController ?? this.confirmPasswordController,
      forgotStatus: forgotStatus ?? this.forgotStatus,
      selectedCountry: selectedCountry ?? this.selectedCountry,
    );
  }
}
