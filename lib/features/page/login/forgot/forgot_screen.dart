import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/shared/widgets/animation/scale_animation.dart';
import 'package:wd/shared/widgets/common_card.dart';
import 'package:wd/shared/widgets/text_fields/phone_input_field.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/icon_textfield.dart';
import 'package:wd/shared/widgets/verification_code/verification_code.dart';
import 'package:wd/shared/widgets/language_dropdown.dart';
import 'package:flutter/gestures.dart';

import '../../../../core/utils/system_util.dart';
import 'forgot_cubit.dart';

class ForgotPage extends StatefulWidget {
  const ForgotPage({super.key});

  @override
  State<ForgotPage> createState() => _ForgotPageState();
}

class _ForgotPageState extends State<ForgotPage> {
  static const double _verticalSpacing = 15.0;
  static const double _sectionSpacing = 30.0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.theme.scaffoldBackgroundColor,
      body: _buildSliverLayout(),
    );
  }

  Widget _buildSliverLayout() {
    final screenHeight = MediaQuery.of(context).size.height;
    final headerHeight = screenHeight * 0.36;

    return CustomScrollView(
      slivers: [
        // Header section with title
        SliverPersistentHeader(
          pinned: false,
          floating: false,
          delegate: _ForgotHeaderDelegate(
            minHeight: headerHeight * 0.6, // Minimum height when collapsed
            maxHeight: headerHeight, // Full height when expanded
            title: _buildTitle(),
            subtitle: _buildSubtitle(),
          ),
        ),
        // Content section
        SliverToBoxAdapter(
          child: Container(
            decoration: BoxDecoration(
              color: context.theme.scaffoldBackgroundColor,
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 40.gw),
              child: Column(
                children: [
                  // Tab bar for phone/email selection
                  BlocBuilder<ForgotCubit, ForgotState>(
                    builder: (context, state) {
                      final currentIndex = state.forgotType == LoginType.phone ? 0 : 1;
                      return Center(
                        child: Container(
                          width: 200.gw,
                          height: 44.gw,
                          decoration: BoxDecoration(
                            color: context.colorTheme.foregroundColor,
                            borderRadius: BorderRadius.circular(12.gw),
                          ),
                          padding: EdgeInsets.all(4.gw),
                          child: Row(
                            children: [
                              _buildCustomTabItem("phone".tr(), Assets.loginTabIcon, currentIndex == 0, () {
                                context.read<ForgotCubit>().updateForgotType(LoginType.phone);
                              }),
                              _buildCustomTabItem("email".tr(), Assets.registerTabIcon, currentIndex == 1, () {
                                context.read<ForgotCubit>().updateForgotType(LoginType.email);
                              }),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                  SizedBox(height: _sectionSpacing.gw),
                  // Form content
                  BlocBuilder<ForgotCubit, ForgotState>(
                    builder: (context, state) {
                      return CommonScaleAnimationWidget(
                        children: [
                          if (state.forgotType == LoginType.phone) ...[
                            _buildPhoneInputField(state),
                          ] else ...[
                            _buildEmailInputField(state),
                          ],
                          SizedBox(height: _verticalSpacing.gw),
                          _buildVerificationCodeInputRow(state),
                          SizedBox(height: _verticalSpacing.gw),
                          _buildPasswordInput(state),
                          SizedBox(height: _verticalSpacing.gw),
                          _buildConfirmPasswordInput(state),
                          SizedBox(height: _sectionSpacing.gw),
                          _buildResetButton(),
                          SizedBox(height: 20.gw),
                          _buildBottomText(),
                          SizedBox(height: 40.gw),
                        ],
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Builds the title for the header
  Widget _buildTitle() {
    return BlocBuilder<ForgotCubit, ForgotState>(
      builder: (context, state) {
        return Text(
          "reset".tr(),
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 28.gw,
            fontWeight: FontWeight.w400,
            color: Colors.white,
            height: 1.2,
          ),
        );
      },
    );
  }

  /// Builds the subtitle for the header
  Widget _buildSubtitle() {
    return BlocBuilder<ForgotCubit, ForgotState>(
      builder: (context, state) {
        final forgotType = state.forgotType;
        String subtitle;

        switch (forgotType) {
          case LoginType.phone:
            subtitle = "reset_password_by_phone".tr();
            break;
          case LoginType.email:
            subtitle = "reset_password_by_email".tr();
            break;
          default:
            subtitle = "password".tr();
        }

        return Text(
          subtitle,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 28.gw,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            height: 1.2,
          ),
        );
      },
    );
  }

  /// Builds the phone input field with country code dropdown
  Widget _buildPhoneInputField(ForgotState state) {
    return PhoneInputField(
      controller: state.phoneController,
      hintText: "hint_enter_phone".tr(),
      selectedCountry: state.selectedCountry,
      onCountryChanged: (country) {
        context.read<ForgotCubit>().updateSelectedCountry(country);
      },
      onChanged: (value) => context.read<ForgotCubit>().setPhone(value),
    );
  }

  /// Builds the email input field
  Widget _buildEmailInputField(ForgotState state) {
    return IconTextfield(
      controller: state.emailController,
      hintText: "enter_email_placeholder".tr(),
      prefixIcon: IconButton(
        icon: Image.asset(Assets.iconMail, width: 20.gw, height: 20.gw),
        onPressed: () {},
      ),
      onChanged: (value) => context.read<ForgotCubit>().setEmail(value),
    );
  }

  /// Builds the verification code input
  Widget _buildVerificationCodeInputRow(ForgotState state) {
    return IconTextfield(
      controller: state.smsCodeController,
      hintText: "please_enter_the_code".tr(),
      keyboardType: TextInputType.number,
      prefixIcon: _buildVerificationPrefix(),
      suffixIcon: VerificationCode(
        phone: state.forgotType == LoginType.phone ? state.phone : '',
        email: state.forgotType == LoginType.email ? state.email : null,
        isEmailMode: state.forgotType == LoginType.email,
        checkIsBind: false, // For forgot password, don't check if phone is bound
        isGradient: false,
        areaCode: state.selectedCountry?.areaCode,
        onSmsCode: (smsCode) {
          if (kDebug && smsCode.isNotEmpty) {
            state.smsCodeController.text = smsCode;
          }
        },
        onEmailCode: (emailCode) {
          if (kDebug && emailCode.isNotEmpty) {
            state.smsCodeController.text = emailCode;
          }
        },
      ),
      suffixPadding: EdgeInsets.symmetric(horizontal: 15.gw, vertical: 15.gh),
      onChanged: (value) => context.read<ForgotCubit>().setSmsCode(value),
    );
  }

  /// Builds verification prefix icon
  Widget _buildVerificationPrefix() {
    return Padding(
      padding: EdgeInsets.all(18.gw),
      child: Image.asset(
        Assets.iconLoginShield,
        width: 20.gw,
        height: 24.gw,
      ),
    );
  }

  /// Builds the password input field
  Widget _buildPasswordInput(ForgotState state) {
    return BlocBuilder<ForgotCubit, ForgotState>(
      buildWhen: (previous, current) => previous.isPasswordVisible != current.isPasswordVisible,
      builder: (context, state) {
        return IconTextfield(
          controller: state.passwordController,
          hintText: "password_must_be_6_22".tr(),
          obscureText: !state.isPasswordVisible,
          prefixIcon: IconButton(
            icon: Image.asset(Assets.iconLoginPassword, width: 20.gw, height: 20.gw),
            onPressed: () {},
          ),
          suffixIcon: GestureDetector(
            onTap: () => context.read<ForgotCubit>().togglePasswordVisibility(),
            child: Container(
              width: 24.gw,
              height: 24.gw,
              alignment: Alignment.center,
              child: SvgPicture.asset(
                "assets/images/login/icon_password_${state.isPasswordVisible ? "" : "in"}visible.svg",
                width: 20.gw,
                height: 20.gw,
              ),
            ),
          ),
          onChanged: (value) => context.read<ForgotCubit>().setPassword(value),
        );
      },
    );
  }

  /// Builds the confirm password input field
  Widget _buildConfirmPasswordInput(ForgotState state) {
    return BlocBuilder<ForgotCubit, ForgotState>(
      buildWhen: (previous, current) => previous.isPasswordVisible != current.isPasswordVisible,
      builder: (context, state) {
        return IconTextfield(
          controller: state.confirmPasswordController,
          hintText: "confirm_password".tr(),
          obscureText: !state.isPasswordVisible,
          prefixIcon: IconButton(
            icon: Image.asset(Assets.iconLoginPassword, width: 20.gw, height: 20.gw),
            onPressed: () {},
          ),
          suffixIcon: GestureDetector(
            onTap: () => context.read<ForgotCubit>().togglePasswordVisibility(),
            child: Container(
              width: 24.gw,
              height: 24.gw,
              alignment: Alignment.center,
              child: SvgPicture.asset(
                "assets/images/login/icon_password_${state.isPasswordVisible ? "" : "in"}visible.svg",
                width: 20.gw,
                height: 20.gw,
              ),
            ),
          ),
          onChanged: (value) => context.read<ForgotCubit>().setConfirmPassword(value),
        );
      },
    );
  }

  /// Builds the reset button
  Widget _buildResetButton() {
    return BlocBuilder<ForgotCubit, ForgotState>(
      builder: (context, state) {
        return CommonButton(
          title: "reset_password".tr(),
          textColor: context.colorTheme.btnTitlePrimary,
          showLoading: state.forgotStatus == SimplyNetStatus.loading,
          onPressed: () => context.read<ForgotCubit>().resetPassword(),
        );
      },
    );
  }

  /// Builds a custom tab item with icon and text
  Widget _buildCustomTabItem(String title, String iconAsset, bool isSelected, VoidCallback onTap) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          width: 100.gw,
          height: 36.gw,
          decoration: BoxDecoration(
            color: isSelected ? context.colorTheme.tabItemBgA : Colors.transparent,
            borderRadius: BorderRadius.circular(10.gw),
            border: isSelected ? Border.all(color: context.colorTheme.borderE, width: 1) : null,
          ),
          alignment: Alignment.center,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                iconAsset,
                width: 16.gw,
                height: 16.gw,
              ),
              SizedBox(width: 6.gw),
              Text(
                title,
                style: context.textTheme.primary.copyWith(
                  fontSize: 14.gw,
                  color: isSelected ? context.colorTheme.textPrimary : context.colorTheme.textSecondary,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the bottom text
  Widget _buildBottomText() {
    return CommonCard(
      padding: 10.gw,
      child: RichText(
        textAlign: TextAlign.start,
        text: TextSpan(
          style: context.textTheme.title.w500.ffAne,
          children: [
            TextSpan(text: "password_reset_help_text".tr()),
            TextSpan(
              text: "online_support".tr(),
              style: context.textTheme.secondary.copyWith(
                decoration: TextDecoration.underline,
              ),
              recognizer: TapGestureRecognizer()..onTap = () => SystemUtil.contactService(),
            ),
            const TextSpan(text: ' '),
          ],
        ),
      ),
    );
  }
}

class _ForgotHeaderDelegate extends SliverPersistentHeaderDelegate {
  final double minHeight;
  final double maxHeight;
  final Widget? title;
  final Widget? subtitle;

  _ForgotHeaderDelegate({
    required this.minHeight,
    required this.maxHeight,
    this.title,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    final progress = (shrinkOffset / (maxHeight - minHeight)).clamp(0.0, 1.0);
    final opacity = 1.0 - progress;

    return Container(
      width: double.infinity,
      height: maxHeight,
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/images/login/bg_login_logo.png'),
          fit: BoxFit.cover,
        ),
      ),
      child: Stack(
        children: [
          // Back button
          Positioned(
            top: MediaQuery.of(context).padding.top + 10.gw,
            left: 15.gw,
            child: GestureDetector(
              onTap: () => Navigator.of(context).pop(),
              child: Container(
                alignment: Alignment.center,
                child: Image.asset(
                  Assets.iconBack,
                  height: 32.gh,
                  width: 32.gw,
                ),
              ),
            ),
          ),
          // Language dropdown
          Positioned(
            top: MediaQuery.of(context).padding.top + 10.gw,
            right: 15.gw,
            child: LanguageDropdown(
              backgroundColor: Colors.white.withOpacity(0.2),
              textColor: Colors.white,
              iconColor: Colors.white,
            ),
          ),
          // Logo and title section
          Positioned.fill(
            child: Opacity(
              opacity: opacity,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(height: 90.gh),
                  // WD Logo
                  SvgPicture.asset(
                    Assets.tabLogo,
                    width: 160.gw,
                    height: 100.gh,
                  ),
                  SizedBox(height: 20.gh),
                  // Title
                  // if (title != null) title!,
                  // SizedBox(height: 8.gh),
                  // // Subtitle
                  // if (subtitle != null) subtitle!,
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  double get maxExtent => maxHeight;

  @override
  double get minExtent => minHeight;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return oldDelegate is! _ForgotHeaderDelegate ||
        oldDelegate.minHeight != minHeight ||
        oldDelegate.maxHeight != maxHeight ||
        oldDelegate.title != title ||
        oldDelegate.subtitle != subtitle;
  }
}
