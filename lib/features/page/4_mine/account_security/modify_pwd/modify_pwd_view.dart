import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/common_card.dart';
import 'package:wd/shared/widgets/icon_textfield.dart';

import 'modify_pwd_cubit.dart';
import 'modify_pwd_state.dart';

enum SetPasswordType { modifyLoginPwd, modifyFundPwd, setFundPwd }

class ModifyPwdPage extends BasePage {
  final SetPasswordType type;

  const ModifyPwdPage({super.key, required this.type});

  @override
  BasePageState<BasePage> getState() => _ModifyPwdPageState();
}

class _ModifyPwdPageState extends BasePageState<ModifyPwdPage> {
  final loginPwdFormatters = [
    LengthLimitingTextInputFormatter(22),
  ];

  final fundPwdFormatters = [
    LengthLimitingTextInputFormatter(6),
    FilteringTextInputFormatter.digitsOnly,
  ];

  bool isLoginType = false;

  @override
  void initState() {
    isLoginType = widget.type == SetPasswordType.modifyLoginPwd;
    switch (widget.type) {
      case SetPasswordType.modifyLoginPwd:
        pageTitle = 'modify_login_password_title'.tr();
        break;
      case SetPasswordType.modifyFundPwd:
        pageTitle = 'modify_fund_password_title'.tr();
        break;

      case SetPasswordType.setFundPwd:
        pageTitle = 'set_fund_password_title'.tr();
        break;
    }
    super.initState();
  }

  Widget _getPasswordTextField({
    required TextEditingController controller,
    required bool isPwdVisible,
    required Function(String) onChanged,
    required VoidCallback onToggleVisibility,
    required String hintText,
    required TextInputType keyboardType,
    required List<TextInputFormatter> inputFormatters,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        IconTextfield(
          controller: controller,
          hintText: hintText,
          hintStyle: context.textTheme.highlight,
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          onChanged: onChanged,
          obscureText: !isPwdVisible,
          textStyle: context.textTheme.regular.fs16,
          prefixIcon: IconButton(
            icon: SvgPicture.asset(
              Assets.iconLockSvg,
              width: 24.gw,
              height: 24.gw,
            ),
            onPressed: null,
          ),
          suffixIcon: GestureDetector(
            onTap: onToggleVisibility,
            child: Container(
              width: 24.gw,
              height: 24.gw,
              alignment: Alignment.center,
              child: SvgPicture.asset(
                "assets/images/login/icon_password_${isPwdVisible ? "" : "in"}visible.svg",
                width: 20.gw,
                height: 20.gw,
              ),
            ),
          ),
        )
      ],
    );
  }

  Widget buildOldPwdTextField() {
    final hintText = widget.type == SetPasswordType.setFundPwd
        ? "login_password"
        : isLoginType
            ? 'old_login_password'
            : 'old_fund_password';

    final keyboardType = widget.type != SetPasswordType.modifyFundPwd ? TextInputType.text : TextInputType.number;
    final inputFormatters = widget.type != SetPasswordType.modifyFundPwd ? loginPwdFormatters : fundPwdFormatters;

    return BlocBuilder<ModifyPwdCubit, ModifyPwdState>(
      buildWhen: (previous, current) => previous.isOldPwdVisible != current.isOldPwdVisible,
      builder: (context, state) {
        return _getPasswordTextField(
          controller: state.oldPwdController,
          isPwdVisible: state.isOldPwdVisible,
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          onChanged: (value) => context.read<ModifyPwdCubit>().oldPwdChanged(value),
          onToggleVisibility: () => context.read<ModifyPwdCubit>().toggleOldPwdVisibility(),
          hintText: hintText.tr(),
        );
      },
    );
  }

  Widget buildNewPwdTextField() {
    final hintText = isLoginType ? 'new_login_password' : 'new_fund_password';
    final keyboardType = isLoginType ? TextInputType.text : TextInputType.number;
    final inputFormatters = isLoginType ? loginPwdFormatters : fundPwdFormatters;

    return BlocBuilder<ModifyPwdCubit, ModifyPwdState>(
      buildWhen: (previous, current) => previous.isNewPwdVisible != current.isNewPwdVisible,
      builder: (context, state) {
        return _getPasswordTextField(
          controller: state.newPwdController,
          isPwdVisible: state.isNewPwdVisible,
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          onChanged: (value) => context.read<ModifyPwdCubit>().newPwdChanged(value),
          onToggleVisibility: () => context.read<ModifyPwdCubit>().toggleNewPwdVisibility(),
          hintText: hintText.tr(),
        );
      },
    );
  }

  Widget buildNewPwdConfirmTextField() {
    final hintText = isLoginType ? 'new_login_password' : 'new_fund_password';
    final keyboardType = isLoginType ? TextInputType.text : TextInputType.number;
    final inputFormatters = isLoginType ? loginPwdFormatters : fundPwdFormatters;

    return BlocBuilder<ModifyPwdCubit, ModifyPwdState>(
      buildWhen: (previous, current) => previous.isNewPwdConfirmVisible != current.isNewPwdConfirmVisible,
      builder: (context, state) {
        return _getPasswordTextField(
          controller: state.newPwdConfirmController,
          isPwdVisible: state.isNewPwdConfirmVisible,
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          onChanged: (value) => context.read<ModifyPwdCubit>().newPwdConfirmChanged(value),
          onToggleVisibility: () => context.read<ModifyPwdCubit>().toggleNewPwdConfirmVisibility(),
          hintText: '${'confirm'.tr()} ${hintText.tr()}',
        );
      },
    );
  }

  Widget mainPageWidget() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: LayoutBuilder(
        builder: (context, constraints) {
          return SingleChildScrollView(
            reverse: true, // Ensures input fields are visible when keyboard opens
            child: ConstrainedBox(
              constraints: BoxConstraints(minHeight: constraints.maxHeight),
              child: IntrinsicHeight(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CommonCard(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          buildOldPwdTextField(),
                          SizedBox(height: 20.gw),
                          buildNewPwdTextField(),
                          SizedBox(height: 20.gw),
                          buildNewPwdConfirmTextField(),
                        ],
                      ),
                    ),
                    const Spacer(),
                    Padding(
                      padding: EdgeInsets.only(
                        bottom: MediaQuery.of(context).viewInsets.bottom + 16,
                        top: 16,
                      ),
                      child: CommonButton(
                        title: "confirm".tr(),
                        onPressed: () => context.read<ModifyPwdCubit>().onClickModifyPassword(widget.type),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  @override
  Widget buildPage(BuildContext context) {
    return mainPageWidget();
  }
}
